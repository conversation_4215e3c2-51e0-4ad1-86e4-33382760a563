# 打包命令
# 有窗口模式
# pyinstaller -F --name=longyuan_power_safety_app --distpath=D:\ --onefile no_weixin.py
# 无窗口模式
# pyinstaller --name=WindPowerSafetyApp --distpath=D:\指定路径 --onefile --noconsole no_weixin.py

import subprocess
import requests
import json
import shutil
from flask import Flask, render_template, request, jsonify, session, send_from_directory
from selenium import webdriver
from selenium.webdriver.common.by import By
from selenium.webdriver.edge.service import Service
from selenium.webdriver.support.ui import WebDriverWait
from selenium.webdriver.support import expected_conditions as EC
from selenium.common.exceptions import TimeoutException, NoSuchElementException, WebDriverException
import time
import os
import cv2
import numpy as np
import base64
from PIL import Image
import threading
import io
import datetime
import re

# 用于存储作业信息
job_info_list = []
# 存储是否为二级公司的选项
is_secondary_company = False
# 存储异常图片信息
abnormal_images = []

# 全局变量存储驱动和状态
global_driver = None
monitoring_active = False
monitoring_threads = []

# 全局锁，用于保护共享资源
lock = threading.Lock()

# 使用全局变量存储任务状态和结果
task_status = {
    "is_complete": False,
    "has_abnormal": False,
    "result": ""
}

# 图片保存目录
SCREENSHOTS_DIR = os.path.join(os.getcwd(), "screenshots")
os.makedirs(SCREENSHOTS_DIR, exist_ok=True)

# 创建错误图像占位符
def create_error_image():
    error_img_dir = os.path.join(os.getcwd(), "static", "img")
    os.makedirs(error_img_dir, exist_ok=True)
    error_img_path = os.path.join(error_img_dir, "image_error.png")
    
    # 如果错误图像已存在则跳过
    if os.path.exists(error_img_path):
        return
        
    try:
        # 创建一个简单的图像占位符
        img = np.ones((300, 400, 3), dtype=np.uint8) * 240  # 浅灰色背景
        
        # 绘制红色叉叉
        cv2.line(img, (50, 50), (350, 250), (0, 0, 255), 5)
        cv2.line(img, (350, 50), (50, 250), (0, 0, 255), 5)
        
        # 添加文本
        font = cv2.FONT_HERSHEY_SIMPLEX
        cv2.putText(img, "Image Error", (100, 150), font, 1, (0, 0, 0), 2, cv2.LINE_AA)
        
        # 保存图像
        cv2.imwrite(error_img_path, img)
        print(f"已创建错误图像占位符: {error_img_path}")
    except Exception as e:
        print(f"创建错误图像占位符失败: {str(e)}")

# 调试模式 - 设置为True会保存所有图片用于调试
DEBUG_MODE = True

app = Flask(__name__)
app.secret_key = 'wind_power_safety_app_key'
app.config['UPLOAD_FOLDER'] = SCREENSHOTS_DIR

@app.route('/')
def index():
    """渲染主页面"""
    # 重置任务状态
    global task_status
    task_status = {
        "is_complete": False,
        "has_abnormal": False,
        "result": ""
    }
    return render_template('index.html')

@app.route('/login', methods=['POST'])
def login_handler():
    """处理登录请求"""
    global is_secondary_company
    username = request.form.get('username')
    password = request.form.get('password')
    is_secondary_company = request.form.get('is_secondary_company') == 'true'
    
    if not username or not password:
        return jsonify({'status': 'error', 'message': '用户名和密码不能为空！'})
    
    # 将任务放入后台线程执行
    thread = threading.Thread(target=start_task, args=(username, password))
    thread.daemon = True
    thread.start()
    
    return jsonify({'status': 'success', 'message': '登录成功，开始处理任务'})

@app.route('/status')
def get_status():
    """获取任务状态"""
    global task_status, abnormal_images
    
    if not task_status["is_complete"]:
        return jsonify({
            'status': 'processing',
            'abnormal_images': get_latest_abnormal_images(5)  # 返回最新的5个异常图片信息
        })
    else:
        # 返回任务结果
        return jsonify({
            'status': 'complete', 
            'result': task_status["result"], 
            'has_abnormal': task_status["has_abnormal"],
            'abnormal_images': get_latest_abnormal_images(5)  # 返回最新的5个异常图片信息
        })

@app.route('/dashboard')
def dashboard():
    """渲染数据分析主页面"""
    return render_template('dashboard.html')

@app.route('/api/abnormal_images')
def get_abnormal_images():
    """获取异常图片列表，支持时间筛选"""
    global abnormal_images
    start_time = request.args.get('start_time')
    end_time = request.args.get('end_time')
    
    filtered_images = abnormal_images
    
    if start_time:
        start_dt = datetime.datetime.strptime(start_time, '%Y-%m-%d')
        filtered_images = [img for img in filtered_images if datetime.datetime.strptime(img['timestamp'].split('_')[0], '%Y%m%d') >= start_dt]
    
    if end_time:
        end_dt = datetime.datetime.strptime(end_time, '%Y-%m-%d')
        filtered_images = [img for img in filtered_images if datetime.datetime.strptime(img['timestamp'].split('_')[0], '%Y%m%d') <= end_dt]
    
    return jsonify(filtered_images)

@app.route('/api/job_info')
def get_job_info():
    """获取所有作业信息"""
    global job_info_list
    return jsonify(job_info_list)

@app.route('/screenshots/<path:filename>')
def get_screenshot(filename):
    """提供截图文件访问"""
    # 检查是否包含目录
    parts = filename.split('/')
    
    # 如果路径中有多个部分（目录/文件名）
    if len(parts) > 1:
        # 提取目录部分和文件名部分
        directory = '/'.join(parts[:-1])
        file = parts[-1]
        
        # 构建完整路径
        full_path = os.path.join(app.config['UPLOAD_FOLDER'], directory)
        
        # 检查此路径是否存在
        if os.path.exists(os.path.join(full_path, file)):
            return send_from_directory(full_path, file)
    
    # 直接从根目录尝试访问文件
    if os.path.exists(os.path.join(app.config['UPLOAD_FOLDER'], filename)):
        return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
    
    # 如果找不到文件，使用递归搜索
    for root, dirs, files in os.walk(app.config['UPLOAD_FOLDER']):
        if filename in files:
            # 计算子目录相对于UPLOAD_FOLDER的相对路径
            relative_path = os.path.relpath(root, app.config['UPLOAD_FOLDER'])
            if relative_path == '.':  # 如果文件在UPLOAD_FOLDER目录
                return send_from_directory(app.config['UPLOAD_FOLDER'], filename)
            else:  # 如果文件在子目录
                subdir_path = os.path.join(app.config['UPLOAD_FOLDER'], relative_path)
                return send_from_directory(subdir_path, filename)
    
    # 如果找不到文件，返回错误图片
    try:
        return send_from_directory(os.path.join(os.getcwd(), 'static', 'img'), 'image_error.png')
    except:
        # 如果错误图片也找不到，返回404错误
        return "文件不存在", 404

@app.route('/stop_monitoring', methods=['POST'])
def stop_monitoring():
    """停止监控"""
    global monitoring_active
    monitoring_active = False
    return jsonify({'status': 'success', 'message': '监控已停止'})

@app.route('/no_tasks')
def no_tasks():
    """显示无任务页面"""
    return render_template('no_tasks.html')

def sanitize_filename(name):
    """清理文件名，移除不合法字符但保留中文"""
    # 替换Windows文件名不允许的字符
    name = re.sub(r'[\\/*?:"<>|]', "_", name)
    
    # 替换斜杠为下划线
    name = name.replace('/', '_')
    
    # 替换连续的下划线为单个下划线
    name = re.sub(r'_+', '_', name)
    
    # 去除开头和结尾的空格和下划线
    name = name.strip('_ ')
    
    # 限制长度，避免文件路径过长
    if len(name) > 100:
        name = name[:100]
        
    # 如果名称为空，提供默认名称
    if not name:
        name = "unnamed"
    
    return name

def get_latest_abnormal_images(count=5):
    """获取最新的count个异常图片信息"""
    global abnormal_images
    with lock:
        sorted_images = sorted(abnormal_images, key=lambda x: x['timestamp'], reverse=True)
        return sorted_images[:count]

def start_task(username, password):
    """后台执行任务"""
    global job_info_list, task_status, global_driver, monitoring_active
    job_info_list = []  # 清空旧结果
    abnormal_images = []  # 清空旧的异常图片
    monitoring_active = True
    
    try:
        # 检查驱动文件是否存在并获取版本信息
        driver_path = os.path.abspath(r'./edgedriver_win64/msedgedriver.exe')
        print(f"使用的驱动路径: {driver_path}")

        # 检查文件是否存在
        if not os.path.exists(driver_path):
            print(f"错误: 驱动文件不存在于 {driver_path}")
            task_status["is_complete"] = True
            return
        
        # 浏览器配置参数
        options = webdriver.EdgeOptions()
        options.add_argument("--disable-blink-features=AutomationControlled")
        options.add_argument("--start-maximized")  # 初始即最大化
        options.add_experimental_option("excludeSwitches", ["enable-automation"])
        options.add_experimental_option('useAutomationExtension', False)
        options.add_argument('--disable-extensions')
        options.add_argument('--disable-save-password-bubble')
        options.add_argument('--disable-gpu')  # 禁用GPU加速，减少一些潜在问题
        options.add_argument('--disable-popup-blocking')  # 禁用弹窗阻止
        options.add_argument('--disable-notifications')  # 禁用通知
        
        try:
            service = Service(executable_path=driver_path)
            driver = webdriver.Edge(service=service, options=options)
            driver.maximize_window()  # 双重确保最大化
            global_driver = driver

            # 登录流程
            print("开始登录流程...")
            login_flow(driver, username, password)
            print("登录完成，开始获取作业信息...")

            # 获取所有正在工作中的作业并打开标签页
            job_tabs = handle_target_page(driver)

            if not job_tabs or len(job_tabs) == 0:
                print("没有找到正在工作中的作业")
                task_status["is_complete"] = True
                if global_driver:
                    global_driver.quit()
                    global_driver = None
                return
            
            print(f"成功创建 {len(job_tabs)} 个摄像头标签页")
            
            # 启动监控线程
            print("开始启动监控线程...")
            start_monitoring_threads(job_tabs)
            print(f"已启动 {len(monitoring_threads)} 个监控线程")
            
            # 设置标志表示任务开始运行
            task_status["is_complete"] = False
            
            # 主线程等待监控停止
            print("主线程开始等待监控任务...")
            try:
                while monitoring_active:
                    time.sleep(5)
                    # 检查所有监控线程状态
                    active_threads = sum(1 for t in monitoring_threads if t.is_alive())
                    print(f"当前活跃监控线程: {active_threads}/{len(monitoring_threads)}")
                    
                    # 如果所有线程都已结束，退出循环
                    if active_threads == 0 and len(monitoring_threads) > 0:
                        print("所有监控线程已结束，停止监控")
                        monitoring_active = False
                        break
            except KeyboardInterrupt:
                print("接收到终止信号，停止监控")
                monitoring_active = False
            
            # 停止所有监控线程
            print("正在等待所有监控线程结束...")
            for thread in monitoring_threads:
                if thread.is_alive():
                    thread.join(2)
            
            # 任务结束，清理资源
            if global_driver:
                print("关闭浏览器驱动...")
                global_driver.quit()
                global_driver = None
            
            # 标记任务完成
            task_status["is_complete"] = True
            task_status["has_abnormal"] = len(abnormal_images) > 0
            print(f"监控任务完成，共检测到 {len(abnormal_images)} 个异常")
            
        except Exception as e:
            print(f"执行任务过程中出现错误: {str(e)}")
            import traceback
            traceback.print_exc()
            if global_driver:
                try:
                    global_driver.quit()
                except:
                    pass
                global_driver = None
            task_status["is_complete"] = True
            
    except Exception as e:
        print(f"启动任务时出错: {str(e)}")
        import traceback
        traceback.print_exc()
        task_status["is_complete"] = True

def login_flow(driver, username, password):
    """处理登录流程"""
    login_url = "https://id.ceic.com/auth/realms/sh4a/protocol/openid-connect/auth?client_id=JTajhxt&redirect_uri=http%3A%2F%2Fsafety.ceic.com&response_type=code&scope=openid&state=1918223715797377024"

    # 访问登录页
    driver.get(login_url)
    wait = WebDriverWait(driver, 20)
    
    # 处理可能出现的任何警告
    try:
        alert = driver.switch_to.alert
        print(f"检测到警告: {alert.text}")
        alert.accept()  # 点击确定按钮
        time.sleep(1)
    except Exception as e:
        # 没有警告弹窗，继续正常流程
        pass
    
    # 输入凭据
    enter_credentials(wait, username, password)
    
    # 处理登录后可能出现的警告
    try:
        alert = WebDriverWait(driver, 3).until(EC.alert_is_present())
        print(f"登录后检测到警告: {alert.text}")
        alert.accept()
    except Exception:
        pass

    # 等待公司Logo出现（标志登录成功或页面加载完成）
    try:
        wait.until(
            EC.presence_of_element_located((By.XPATH, '//*[@id="app"]/div[1]/div[2]/div[1]/img'))
        )
        print("登录成功")
    except Exception as e:
        print(f"等待Logo出现时出错: {str(e)}")
        # 检查是否有警告
        try:
            alert = driver.switch_to.alert
            print(f"发现警告: {alert.text}")
            alert.accept()
        except:
            pass

def enter_credentials(wait, username, password):
    """输入登录信息"""
    try:
        username_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "#username"))
        )
        username_field.clear()
        username_field.send_keys(username)

        password_field = wait.until(
            EC.visibility_of_element_located((By.CSS_SELECTOR, "#password"))
        )
        password_field.clear()
        password_field.send_keys(password)

        # 点击登录按钮
        wait.until(
            EC.element_to_be_clickable((By.XPATH, '//*[@id="kc-submit"]'))
        ).click()
    except Exception as e:
        # 检查是否有意外警告弹出
        try:
            alert = wait._driver.switch_to.alert
            print(f"处理意外警告: {alert.text}")
            alert.accept()  # 点击确定
            # 刷新页面重试
            wait._driver.refresh()
            time.sleep(2)
            # 递归调用自身重试
            enter_credentials(wait, username, password)
            return
        except Exception:
            # 如果不是警告问题，重新抛出原始异常
            raise e

    # 判断是否存在特定元素并点击
    try:
        wait.until(
            EC.element_to_be_clickable((By.XPATH, '//*[@id="info_flash"]/div/div[3]/a[2]')),
        ).click()
    except (TimeoutException, NoSuchElementException):
        # 若元素不存在或超时未出现，跳过该步骤
        pass

def handle_target_page(driver):
    """处理目标页面，找出所有正在工作中的作业"""
    global is_secondary_company, job_info_list
    target_url = "http://safety.ceic.com/machinery/20/40"
    job_tabs = []
    
    wait = WebDriverWait(driver, 20)
    
    # 访问目标页面
    driver.get(target_url)
    
    # 等待页面加载完成
    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))

    try:
        # 点击分页设置
        pagination_dropdown = wait.until(
            EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'pagination')]//input"))
        )
        driver.execute_script("arguments[0].click();", pagination_dropdown)

        # 选择50条/页
        select_option = wait.until(
            EC.visibility_of_element_located((By.XPATH, "//li[contains(.,'50条/页')]"))
        )
        driver.execute_script("arguments[0].click();", select_option)

        # 等待数据加载
        wait.until(
            EC.invisibility_of_element_located((By.CSS_SELECTOR, ".el-loading-mask"))
        )

        # 定位tbody并获取所有行
        tbody = wait.until(EC.presence_of_element_located((By.XPATH, "//tbody")))

        # 获取所有"作业中"的行索引
        job_in_progress_indexes = []
        rows = tbody.find_elements(By.XPATH, "./tr")
        for index, row in enumerate(rows, start=1):  # 索引从1开始
            try:
                # 获取最后一个td的状态
                last_td = row.find_element(By.XPATH, "./td[last()]")
                status = last_td.text.strip()

                if status == "作业中":
                    job_in_progress_indexes.append(index)

            except NoSuchElementException:
                continue  # 跳过异常行

        # 添加无任务时的提示
        if not job_in_progress_indexes:
            print("当前没有正在工作中的任务")
            return []

        # 根据二级公司选项确定要使用的td索引
        td_index = 4 if is_secondary_company else 3

        # 为每个作业创建一个标签页
        main_window = driver.current_window_handle
        
        for row_index in job_in_progress_indexes:
            try:
                print(f"处理第{row_index}行作业")
                
                # 重新获取tbody和所有行
                tbody = wait.until(EC.presence_of_element_located((By.XPATH, "//tbody")))
                rows = tbody.find_elements(By.XPATH, "./tr")
                row = rows[row_index - 1]

                # 获取作业名称和地点信息用于后续使用
                try:
                    job_name_td = row.find_element(By.XPATH, f"./td[2]")
                    job_name = job_name_td.text.strip()
                    job_location_td = row.find_element(By.XPATH, f"./td[3]")
                    job_location = job_location_td.text.strip()
                except Exception as e:
                    print(f"获取作业信息失败: {e}")
                    job_name = f"作业{row_index}"
                    job_location = "未知地点"

                # 根据二级公司选项获取对应列
                target_td = row.find_element(By.XPATH, f"./td[{td_index}]")
                link = target_td.find_element(By.XPATH, ".//a")

                # 滚动到目标元素位置
                driver.execute_script("arguments[0].scrollIntoView(true);", link)
                time.sleep(1)  # 等待滚动完成

                # 点击链接打开作业详情
                try:
                    driver.execute_script("arguments[0].click();", link)
                    wait.until(EC.presence_of_element_located((By.XPATH, '//div[contains(@class,"app-container")]')))
                except Exception as e:
                    print(f"打开作业详情失败: {e}")
                    continue

                # 等待作业详情页面加载
                time.sleep(3)

                # 获取摄像头列表
                camera_tabs = []
                try:
                    # 点击打开摄像头列表
                    wait.until(EC.presence_of_element_located((By.XPATH, 
                        '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input'))).click()
                    
                    # 等待摄像头列表出现
                    ul_element = wait.until(EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/div[1]/div[1]/ul")))
                    camera_elements = ul_element.find_elements(By.TAG_NAME, "li")
                    camera_names = []
                    
                    # 确保正确获取摄像头名称
                    for li in camera_elements:
                        try:
                            name = li.find_element(By.XPATH, ".//span").text.strip()
                            print(f"发现摄像头: '{name}'")
                            camera_names.append(name)
                        except Exception as e:
                            print(f"获取摄像头名称失败: {str(e)}")
                            camera_names.append(f"未命名摄像头_{len(camera_names)+1}")
                    
                    # 记录作业信息
                    job_info = {
                        "job_name": job_name,
                        "job_location": job_location,
                        "cameras": camera_names
                    }
                    job_info_list.append(job_info)
                    
                    # 获取当前URL（作业页面URL）
                    job_page_url = driver.current_url
                    
                    # 为每个摄像头创建一个标签页
                    for i, camera_element in enumerate(camera_elements):
                        camera_name = camera_names[i]
                        
                        try:
                            # 先在当前标签页点击摄像头，获取加载后的URL
                            driver.execute_script("arguments[0].click();", camera_element)
                            time.sleep(3)  # 等待摄像头加载
                            
                            # 获取摄像头的URL
                            camera_page_url = driver.current_url
                            print(f"摄像头 {job_name}/{camera_name} URL: {camera_page_url}")
                            
                            # 打开新标签页并导航到摄像头URL
                            driver.execute_script("window.open(arguments[0]);", camera_page_url)
                            time.sleep(1)  # 确保标签页已创建
                            
                            # 获取新标签页的句柄
                            new_tab = driver.window_handles[-1]
                            
                            # 切换到新标签页确认加载
                            driver.switch_to.window(new_tab)
                            if driver.current_url != camera_page_url:
                                print(f"标签页URL不匹配，重新导航: {driver.current_url} != {camera_page_url}")
                                driver.get(camera_page_url)
                                time.sleep(2)
                            
                            # 确保摄像头名称有效
                            camera_display_name = camera_name if camera_name and camera_name.strip() else f"{job_name}_摄像头{i+1}"
                            print(f"保存标签页信息 - 作业: {job_name}, 摄像头: {camera_display_name}")
                            
                            # 将标签页信息保存到列表中
                            camera_tabs.append({
                                "tab": new_tab,
                                "job_name": job_name,
                                "job_location": job_location,
                                "camera_name": camera_display_name,
                                "url": camera_page_url
                            })
                            
                            # 切回原始标签页
                            driver.switch_to.window(main_window)
                            
                            # 返回作业页面
                            if driver.current_url != job_page_url:
                                driver.get(job_page_url)
                                time.sleep(2)
                            
                            # 重新打开摄像头列表
                            wait.until(EC.element_to_be_clickable((By.XPATH, 
                                '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input'))).click()
                            time.sleep(1)
                            
                            # 重新获取摄像头列表元素
                            ul_element = wait.until(EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/div[1]/div[1]/ul")))
                            camera_elements = ul_element.find_elements(By.TAG_NAME, "li")
                        
                        except Exception as e:
                            print(f"处理摄像头 {camera_name} 时出错: {str(e)}")
                            # 尝试返回作业页面
                            try:
                                driver.get(job_page_url)
                                time.sleep(2)
                                wait.until(EC.element_to_be_clickable((By.XPATH, 
                                    '//*[@id="app"]/div[1]/div[3]/section/div/main/div[1]/div[2]/div[1]/div[2]/div[2]/div[1]/div/input'))).click()
                                time.sleep(1)
                                ul_element = wait.until(EC.presence_of_element_located((By.XPATH, "/html/body/div[2]/div[1]/div[1]/ul")))
                                camera_elements = ul_element.find_elements(By.TAG_NAME, "li")
                            except Exception as recover_error:
                                print(f"尝试恢复失败: {str(recover_error)}")
                    
                    job_tabs.extend(camera_tabs)
                    
                    # 返回主页面
                    driver.get(target_url)
                    
                    # 等待页面加载
                    wait.until(EC.presence_of_element_located((By.TAG_NAME, "body")))
                    
                    # 重新设置分页
                    pagination_dropdown = wait.until(
                        EC.element_to_be_clickable((By.XPATH, "//div[contains(@class,'pagination')]//input"))
                    )
                    driver.execute_script("arguments[0].click();", pagination_dropdown)
                    
                    select_option = wait.until(
                        EC.visibility_of_element_located((By.XPATH, '/html/body/div[2]/div[1]/div[1]/ul/li[4]'))
                    )
                    driver.execute_script("arguments[0].click();", select_option)
                    
                    # 等待数据加载
                    wait.until(
                        EC.invisibility_of_element_located((By.CSS_SELECTOR, ".el-loading-mask"))
                    )
                    
                except Exception as e:
                    print(f"处理作业摄像头时出错: {str(e)}")
                    continue

            except Exception as e:
                print(f"处理第{row_index}行时出错: {str(e)}")
                continue

        return job_tabs

    except Exception as e:
        print(f"处理目标页面时出错: {str(e)}")
        return []

def start_monitoring_threads(job_tabs):
    """为每个标签页启动一个监控线程"""
    global monitoring_threads
    
    for tab_info in job_tabs:
        thread = threading.Thread(
            target=monitor_camera, 
            args=(tab_info,)
        )
        thread.daemon = True
        monitoring_threads.append(thread)
        thread.start()

def monitor_camera(tab_info):
    """监控特定摄像头"""
    global global_driver, monitoring_active, abnormal_images
    
    if not global_driver:
        print("驱动未初始化，无法监控")
        return
    
    driver = global_driver
    wait = WebDriverWait(driver, 10)
    
    job_name = tab_info["job_name"]
    job_location = tab_info["job_location"]
    camera_name = tab_info["camera_name"]
    tab = tab_info["tab"]
    camera_url = tab_info["url"]
    
    # 创建摄像头专用目录，确保目录名包含足够信息
    # 检查摄像头名称是否为空或不完整
    if not camera_name or camera_name.strip() == "":
        safe_dir_name = sanitize_filename(f"{job_name}_{job_location}")
    else:
        safe_dir_name = sanitize_filename(f"{job_name}_{camera_name}")
    
    # 打印调试信息
    print(f"原始摄像头名称: '{camera_name}'")
    print(f"生成的目录名: '{safe_dir_name}'")
    
    camera_dir = os.path.join(SCREENSHOTS_DIR, safe_dir_name)
    os.makedirs(camera_dir, exist_ok=True)
    
    # 记录原始名称到安全名称的映射，用于前端显示
    camera_info = {
        "job_name": job_name,
        "job_location": job_location, 
        "camera_name": camera_name,
        "safe_dir_name": safe_dir_name
    }
    
    # 切换到对应标签页
    try:
        driver.switch_to.window(tab)
        # 确保页面已加载，如果是about:blank或加载不完整，重新加载URL
        if driver.current_url == "about:blank" or not driver.current_url.startswith("http"):
            driver.get(camera_url)
            time.sleep(3)  # 等待页面加载
    except Exception as e:
        print(f"切换到标签页失败: {str(e)}")
        return
        
    # 查找播放器元素，尝试多次
    player_found = False
    retry_attempts = 3
    
    for attempt in range(retry_attempts):
        try:
            # 定位视频播放元素
            target_element = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="player"]')))
            player_found = True
            print(f"在标签页 {job_name}/{camera_name} 中成功找到播放器元素")
            break
        except Exception as e:
            print(f"尝试 {attempt+1}/{retry_attempts} - 未找到播放器元素: {str(e)}")
            
            # 如果不是最后一次尝试，重新加载页面
            if attempt < retry_attempts - 1:
                try:
                    driver.refresh()
                    time.sleep(3)
                except Exception as refresh_error:
                    print(f"刷新页面失败: {str(refresh_error)}")
    
    # 如果找不到播放器元素，终止该摄像头的监控
    if not player_found:
        print(f"在标签页 {job_name}/{camera_name} 中无法找到播放器元素，终止监控")
        return
        
    while monitoring_active:
        try:
            # 生成时间戳
            timestamp = datetime.datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # 清理文件名中的特殊字符，确保使用ASCII字符
            safe_job_name = sanitize_filename(job_name)
            safe_job_location = sanitize_filename(job_location)
            safe_camera_name = sanitize_filename(camera_name)
            
            # 构建文件名 - 使用更简单的格式减少编码问题
            simple_filename = f"img_{timestamp}.png"
            filepath = os.path.join(camera_dir, simple_filename)
            
            # 用于调试的文件路径
            debug_filepath = None
            
            # 标记是否有错误发生
            has_error = False
            error_message = ""
            
            # 确保截图目录存在
            os.makedirs(os.path.dirname(filepath), exist_ok=True)
            
            try:
                # 保存调试信息到单独的JSON文件，方便查看和调试
                metadata_file = os.path.join(camera_dir, f"info_{timestamp}.json")
                metadata = {
                    "job_name": job_name,
                    "job_location": job_location,
                    "camera_name": camera_name,
                    "timestamp": timestamp,
                    "filename": simple_filename,
                    "path": filepath
                }
                
                try:
                    with open(metadata_file, 'w', encoding='utf-8') as f:
                        json.dump(metadata, f, ensure_ascii=False, indent=2)
                except Exception as e:
                    print(f"保存元数据失败: {str(e)}")
                
                # 定位视频播放元素
                target_element = wait.until(EC.presence_of_element_located((By.XPATH, '//*[@id="player"]')))
                
                # 滚动到视频元素可见位置
                driver.execute_script("arguments[0].scrollIntoView(true);", target_element)
                time.sleep(0.5)
                
                # 获取元素位置和大小
                location = target_element.location
                size = target_element.size
                
                # 考虑页面滚动
                scroll_x = driver.execute_script(
                    'return window.pageXOffset || document.documentElement.scrollLeft || document.body.scrollLeft || 0;')
                scroll_y = driver.execute_script(
                    'return window.pageYOffset || document.documentElement.scrollTop || document.body.scrollTop || 0;')
                scale = driver.execute_script('return window.devicePixelRatio || 1;')
                
                # 计算截图区域
                left = (location['x'] + scroll_x) * scale
                top = (location['y'] + scroll_y) * scale
                right = left + (size['width'] * scale)
                bottom = top + (size['height'] * scale)
                
                print(f"准备截图 {job_name}/{camera_name}: 位置={location}, 大小={size}, 滚动=({scroll_x},{scroll_y}), 缩放={scale}")
                
                # 截取整个页面
                full_page_filepath = filepath.replace(".png", "_fullpage.png")
                driver.save_screenshot(full_page_filepath)
                print(f"整页截图已保存: {full_page_filepath}")
                
                # 裁剪图像
                try:
                    with Image.open(full_page_filepath) as img:
                        # 检查计算的裁剪区域是否有效
                        img_width, img_height = img.size
                        print(f"截图尺寸: {img_width} x {img_height}, 裁剪区域: left={left}, top={top}, right={right}, bottom={bottom}")
                        
                        # 确保裁剪区域在图像范围内
                        left = max(0, min(left, img_width-1))
                        top = max(0, min(top + 100, img_height-1))
                        right = max(left+1, min(right, img_width))
                        bottom = max(top+1, min(bottom, img_height))
                        
                        print(f"调整后的裁剪区域: left={left}, top={top}, right={right}, bottom={bottom}")
                        
                        # 裁剪并保存
                        cropped_img = img.crop((int(left), int(top), int(right), int(bottom)))
                        cropped_img.save(filepath)
                        print(f"裁剪后图像已保存: {filepath}")
                        
                        # 删除全页截图，除非是调试模式
                        if not DEBUG_MODE and os.path.exists(full_page_filepath):
                            os.remove(full_page_filepath)
                except Exception as crop_error:
                    has_error = True
                    error_message = f"裁剪图像出错: {str(crop_error)}"
                    print(error_message)
                    # 保留全页截图用于调试
                    debug_filepath = full_page_filepath
                    # 如果裁剪失败，直接使用全页截图
                    filepath = full_page_filepath
                
                # 分析图像
                try:
                    status = check_camera_status(filepath)
                    print(f"{filepath} 的分析结果: {'异常' if status == 0 else '正常'}")
                    
                    # 如果图像异常或处于调试模式，保存记录
                    if status == 0 or DEBUG_MODE:  # 0表示异常
                        with lock:
                            abnormal_images.append({
                                "filepath": os.path.basename(filepath),
                                "filepath_full": filepath,  # 添加完整路径便于调试
                                "dir": safe_dir_name,  # 添加目录名方便访问
                                "job_name": job_name,
                                "job_location": job_location,
                                "camera_name": camera_name,
                                "timestamp": timestamp,
                                "reason": "摄像头黑屏" if status == 0 else "调试模式"
                            })
                            print(f"{'检测到异常' if status == 0 else '调试模式'}: {job_name}/{camera_name} - 保存到 {filepath}")
                    else:
                        # 如果不是异常且不是调试模式，删除图片节省空间
                        if os.path.exists(filepath):
                            os.remove(filepath)
                except Exception as analysis_error:
                    has_error = True
                    error_message = f"分析图像出错: {str(analysis_error)}"
                    print(error_message)
                    # 错误时保留图片用于调试
                    with lock:
                        abnormal_images.append({
                            "filepath": os.path.basename(filepath),
                            "filepath_full": filepath,  # 添加完整路径便于调试
                            "dir": safe_dir_name,  # 添加目录名方便访问
                            "job_name": job_name,
                            "job_location": job_location,
                            "camera_name": camera_name,
                            "timestamp": timestamp,
                            "reason": f"分析错误: {str(analysis_error)[:50]}"
                        })
            
            except Exception as e:
                has_error = True
                error_message = f"截图过程出错: {str(e)}"
                print(error_message)
                import traceback
                traceback.print_exc()
                
                # 保存整个页面截图用于调试
                try:
                    debug_filepath = filepath.replace(".png", "_error.png")
                    driver.save_screenshot(debug_filepath)
                    print(f"错误截图已保存: {debug_filepath}")
                    
                    # 记录错误图像
                    with lock:
                        abnormal_images.append({
                            "filepath": os.path.basename(debug_filepath),
                            "filepath_full": debug_filepath, # 添加完整路径
                            "dir": safe_dir_name, # 添加目录名
                            "job_name": job_name,
                            "job_location": job_location,
                            "camera_name": camera_name,
                            "timestamp": timestamp,
                            "reason": f"截图错误: {str(e)[:50]}"
                        })
                except Exception as ss_error:
                    print(f"保存错误截图失败: {str(ss_error)}")
                
                # 尝试刷新页面
                try:
                    driver.refresh()
                    time.sleep(3)
                except Exception as refresh_error:
                    print(f"刷新页面失败: {str(refresh_error)}")
            
            # 等待30秒，或者根据是否出错调整等待时间
            wait_time = 10 if has_error else 30  # 如果有错误，10秒后重试
            for _ in range(wait_time):
                if not monitoring_active:
                    break
                time.sleep(1)
                
        except Exception as e:
            print(f"监控过程出错: {str(e)}")
            import traceback
            traceback.print_exc()
            
            # 如果仍在监控中，尝试刷新页面继续监控
            if monitoring_active:
                try:
                    driver.refresh()
                    time.sleep(3)
                except:
                    # 如果刷新失败，等待一段时间再继续
                    time.sleep(5)
            else:
                break

def check_camera_status(image_path, debug=False):
    """检测摄像头是否正常开启"""
    print(f"开始分析图像: {image_path}")
    
    # 尝试使用PIL库读取图像，而不是OpenCV（处理中文路径更好）
    try:
        from PIL import Image, ImageStat
        pil_image = Image.open(image_path)
        # 转换为OpenCV格式
        image = np.array(pil_image)
        # 如果是RGB图像，转换为BGR (OpenCV使用BGR)
        if len(image.shape) == 3 and image.shape[2] == 3:
            image = image[:, :, ::-1].copy()  # RGB to BGR
        print(f"使用PIL成功读取图像，尺寸: {image.shape}")
    except Exception as pil_error:
        print(f"使用PIL读取图像失败: {str(pil_error)}")
        # 回退到OpenCV
        try:
            max_attempts = 3
            for attempt in range(max_attempts):
                try:
                    image = cv2.imread(image_path)
                    if image is not None:
                        print(f"图像成功读取，尺寸: {image.shape}")
                        break
                    if attempt < max_attempts - 1:
                        print(f"尝试读取图像 {image_path} 失败，第 {attempt + 1} 次重试...")
                        time.sleep(1)
                except Exception as e:
                    print(f"读取图像出错 (尝试 {attempt+1}/{max_attempts}): {str(e)}")
                    if attempt < max_attempts - 1:
                        time.sleep(1)
            else:
                print(f"无法读取图像: {image_path}")
                # 返回错误代码而不是直接返回-1，这样调用者可以知道发生了错误
                raise Exception(f"无法读取图像: {image_path}")
        except Exception as cv_error:
            print(f"OpenCV读取失败: {str(cv_error)}")
            raise Exception(f"无法读取图像: {image_path}")

    try:
        # 保存原始图像的副本用于调试
        if DEBUG_MODE:
            debug_copy = image_path.replace(".png", "_debug.png")
            cv2.imwrite(debug_copy, image)
            print(f"已保存调试用图像副本: {debug_copy}")
        
        height, width = image.shape[:2]
        print(f"图像尺寸: {width} x {height}")
        
        # 如果图像太小，可能是裁剪错误
        if width < 20 or height < 20:
            print(f"图像尺寸过小: {width} x {height}")
            raise Exception("图像尺寸过小，可能是裁剪错误")
        
        x_start, y_start = int(width * 0.1), int(height * 0.1)
        x_end, y_end = int(width * 0.9), int(height * 0.9)
        center_region = image[y_start:y_end, x_start:x_end]
        
        if center_region.size == 0:
            print("中心区域为空")
            raise Exception("中心区域为空")
        
        gray = cv2.cvtColor(center_region, cv2.COLOR_BGR2GRAY)
        avg_brightness = np.mean(gray)
        std_brightness = np.std(gray)
        non_black_ratio = np.sum(gray > 10) / (gray.shape[0] * gray.shape[1])
        color_std = np.std(center_region, axis=(0, 1)).mean()
        edges = cv2.Canny(gray, 50, 150)
        edge_count = np.count_nonzero(edges) / (edges.shape[0] * edges.shape[1])
        hist = cv2.calcHist([gray], [0], None, [256], [0, 256])
        hist_norm = hist / hist.sum()
        dominant_color_ratio = np.max(hist_norm)
        
        print(f"图像分析结果: avg_brightness={avg_brightness:.2f}, std_brightness={std_brightness:.2f}, "
              f"non_black_ratio={non_black_ratio:.2f}, color_std={color_std:.2f}, edge_count={edge_count:.2f}, "
              f"dominant_color_ratio={dominant_color_ratio:.2f}")

        is_closed = False
        if avg_brightness < 15 and std_brightness < 10:
            is_closed = True
            print("判定为异常: 亮度过低且标准差小")
        elif dominant_color_ratio > 0.95 and edge_count < 0.01:
            is_closed = True
            print("判定为异常: 主导颜色比例过高且边缘数量少")
        elif std_brightness < 0.5 and color_std < 0.5:
            is_closed = True
            print("判定为异常: 亮度标准差和颜色标准差都过小")

        print(f"{image_path}的识别结果：{is_closed}")
        
        # 如果处于调试模式，保存带有参数的调试信息
        if DEBUG_MODE:
            # 在原图上添加文字
            debug_img = image.copy()
            info_text = [
                f"Brightness: {avg_brightness:.1f} (std: {std_brightness:.1f})",
                f"Non-black: {non_black_ratio:.2f}",
                f"Color std: {color_std:.2f}",
                f"Edge count: {edge_count:.2f}",
                f"Dom. color: {dominant_color_ratio:.2f}",
                f"Status: {'Abnormal' if is_closed else 'Normal'}"
            ]
            
            y_pos = 30
            for text in info_text:
                cv2.putText(debug_img, text, (10, y_pos), cv2.FONT_HERSHEY_SIMPLEX, 
                           0.7, (0, 0, 255), 2, cv2.LINE_AA)
                y_pos += 30
            
            debug_path = image_path.replace(".png", "_analyzed.png")
            cv2.imwrite(debug_path, debug_img)
            print(f"已保存分析后的图像: {debug_path}")

        return 0 if is_closed else 1
        
    except Exception as e:
        print(f"图像分析过程出错: {str(e)}")
        import traceback
        traceback.print_exc()
        # 返回错误，这样调用者可以知道发生了错误
        raise Exception(f"图像分析失败: {str(e)}")

# 启动应用
if __name__ == '__main__':
    # 确保目录存在
    os.makedirs('templates', exist_ok=True)
    os.makedirs('static', exist_ok=True)
    os.makedirs('static/img', exist_ok=True)
    os.makedirs(SCREENSHOTS_DIR, exist_ok=True)
    
    # 创建错误图像占位符
    create_error_image()
    
    app.run(debug=True, port=5000) 

"""
当前的项目存在几个严重的问题：
1：每个子页面的摄像头名称有时候无法正确获取到
2：每一次进入子页面后，都要滚动一下页面来定位摄像头画面元素，如果不需要滚动就可以定位的话，请你将这个滚动的功能去除
3：因为需要将每一个工作中的每一个摄像头都单独开一个标签页，我发现现在的情况是打开了一个新的标签页后，页面自动刷新，导致所有的标签页还是默认选择的第一个摄像头，流程其实很简单，首先先获取到所有工作中的作业列表，然后依次进入每一个作业，每一个作业的处理分别了以下几步,首先先获取到所有的摄像头名称，主要是在"class="el-scrollbar__view el-select-dropdown__list""的ul中存放着的那些li，记住他们的索引，然后根据当前作业的摄像头数量打开对应数量的这个作业的标签页，然后依次根据各自摄像头的索引点击摄像头列表中的对应摄像头。摄像头的名称就存放在每一个li里面的span中
4：现在保存的中文图像名有乱码，并且为什么不是保存在各自的文件夹中，文件夹中的图像名是数字

请你请你综合所有的问题，然后再详细地分析html页面，@work_subpage.html 这个文件是点击某个工作后进入的子页面，你主要分析这个文件中关于元素标签的结构和获取，其它JavaScript和无关元素请不要分析。

请你完成项目的修改任务
"""
/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
}

body {
    background-color: #f0f2f5;
    min-height: 100vh;
}

.container {
    width: 100%;
    min-height: 100vh;
    display: flex;
    flex-direction: column;
}

/* 头部样式 */
header {
    background-color: #0056b3;
    color: white;
    padding: 15px 20px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.logo-container {
    display: flex;
    align-items: center;
}

.logo {
    width: 40px;
    height: 40px;
    margin-right: 15px;
}

h1 {
    font-size: 20px;
    font-weight: bold;
}

.control-panel {
    display: flex;
    gap: 10px;
}

/* 按钮样式 */
.btn {
    padding: 8px 15px;
    border: none;
    border-radius: 4px;
    cursor: pointer;
    font-size: 14px;
    transition: all 0.3s ease;
}

.btn:hover {
    opacity: 0.9;
}

.primary {
    background-color: #4caf50;
    color: white;
}

.danger {
    background-color: #f44336;
    color: white;
}

/* 仪表板布局 */
.dashboard {
    display: flex;
    height: calc(100vh - 70px);
    padding: 20px;
    gap: 20px;
}

.left-panel {
    width: 60%;
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.right-panel {
    width: 40%;
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    overflow-y: auto;
}

/* 图片展示区域 */
.image-display-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    height: 50%;
    overflow: hidden;
}

.image-display {
    display: flex;
    margin-top: 15px;
    height: calc(100% - 80px);
}

.image-display img {
    max-width: 60%;
    max-height: 100%;
    object-fit: contain;
    border: 1px solid #ddd;
}

.image-info {
    margin-left: 20px;
    flex-grow: 1;
    display: flex;
    flex-direction: column;
    gap: 10px;
}

.image-info p {
    font-size: 14px;
    line-height: 1.5;
}

.image-controls {
    display: flex;
    justify-content: space-between;
    margin-top: 15px;
}

/* 图片列表区域 */
.image-list-container {
    background-color: white;
    border-radius: 8px;
    box-shadow: 0 0 10px rgba(0, 0, 0, 0.1);
    padding: 20px;
    height: 50%;
    overflow: hidden;
    display: flex;
    flex-direction: column;
}

.filter-container {
    margin-bottom: 15px;
}

.date-filter {
    display: flex;
    align-items: center;
    gap: 10px;
}

.date-filter input[type="date"] {
    padding: 5px;
    border: 1px solid #ddd;
    border-radius: 4px;
}

.image-list {
    overflow-y: auto;
    flex-grow: 1;
    border: 1px solid #eee;
    border-radius: 4px;
    padding: 10px;
}

.image-list-item {
    display: flex;
    align-items: center;
    padding: 10px;
    border-bottom: 1px solid #eee;
    cursor: pointer;
    transition: background-color 0.2s;
}

.image-list-item:hover {
    background-color: #f5f5f5;
}

.image-list-item.selected {
    background-color: #e3f2fd;
}

.image-list-item-thumbnail {
    width: 60px;
    height: 60px;
    object-fit: cover;
    margin-right: 15px;
    border: 1px solid #ddd;
}

.image-list-item-info {
    flex-grow: 1;
}

.image-list-item-info h3 {
    font-size: 14px;
    margin-bottom: 5px;
}

.image-list-item-info p {
    font-size: 12px;
    color: #666;
}

/* 作业信息区域 */
.job-list {
    overflow-y: auto;
    max-height: 100%;
}

.job-item {
    margin-bottom: 20px;
    padding-bottom: 20px;
    border-bottom: 1px solid #eee;
}

.job-item:last-child {
    border-bottom: none;
}

.job-item h3 {
    font-size: 16px;
    margin-bottom: 10px;
    color: #0056b3;
}

.job-item-info {
    margin-bottom: 10px;
}

.job-item-info p {
    margin-bottom: 5px;
    font-size: 14px;
}

.camera-list {
    margin-top: 10px;
    padding-left: 20px;
}

.camera-list li {
    margin-bottom: 5px;
    font-size: 14px;
}

/* 加载动画 */
.modal {
    display: none;
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    z-index: 1000;
    justify-content: center;
    align-items: center;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 8px;
    text-align: center;
    display: flex;
    flex-direction: column;
    align-items: center;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0056b3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* 响应式布局 */
@media screen and (max-width: 1200px) {
    .dashboard {
        flex-direction: column;
    }
    
    .left-panel, .right-panel {
        width: 100%;
    }
    
    .left-panel {
        height: auto;
    }
    
    .image-display-container, .image-list-container {
        height: 400px;
    }
    
    .right-panel {
        height: 400px;
    }
} 
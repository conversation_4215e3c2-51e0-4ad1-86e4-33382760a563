/* 全局样式 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
}

body {
    background-color: #f0f2f5;
    height: 100vh;
    overflow: hidden;
    position: relative;
}

.container {
    height: 100vh;
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-image: url('../img/wind_farm_bg.jpg');
    background-size: cover;
    background-position: center;
    position: relative;
}

/* 登录容器样式 */
.login-container {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    padding: 30px;
    width: 400px;
}

.header {
    display: flex;
    align-items: center;
    margin-bottom: 30px;
}

.logo {
    width: 60px;
    height: 60px;
    margin-right: 15px;
}

.header h1 {
    color: #0056b3;
    font-size: 24px;
    font-weight: bold;
}

.login-form {
    display: flex;
    flex-direction: column;
    gap: 20px;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
}

.form-group label {
    font-weight: bold;
    color: #333;
}

input[type="text"], 
input[type="password"] {
    padding: 10px;
    border: 1px solid #ddd;
    border-radius: 4px;
    font-size: 16px;
}

.radio-group {
    display: flex;
    gap: 20px;
}

.radio-group label {
    display: flex;
    align-items: center;
    gap: 5px;
    font-weight: normal;
}

.btn {
    background-color: #0056b3;
    color: white;
    border: none;
    border-radius: 4px;
    padding: 10px 15px;
    font-size: 16px;
    cursor: pointer;
    transition: background-color 0.3s;
}

.btn:hover {
    background-color: #003d82;
}

/* 模态窗口样式 */
.modal {
    display: none;
    position: fixed;
    z-index: 100;
    left: 0;
    top: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
}

.modal-content {
    position: relative;
    background-color: #fefefe;
    margin: 10% auto;
    padding: 0;
    border-radius: 8px;
    box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
    width: 400px;
    animation: fadeIn 0.3s;
}

.result-content {
    width: 500px;
}

.modal-header {
    padding: 15px;
    border-bottom: 1px solid #eee;
    position: relative;
}

.modal-header h2 {
    margin: 0;
    color: #0056b3;
    font-size: 20px;
}

.close {
    position: absolute;
    right: 15px;
    top: 15px;
    font-size: 24px;
    font-weight: bold;
    cursor: pointer;
}

.modal-body {
    padding: 15px;
    max-height: 400px;
    overflow-y: auto;
}

.modal-body pre {
    white-space: pre-wrap;
    font-family: "微软雅黑", "Microsoft YaHei", sans-serif;
    line-height: 1.5;
}

.modal-footer {
    padding: 15px;
    border-top: 1px solid #eee;
    text-align: right;
}

.modal-footer button {
    margin-left: 10px;
}

/* 加载动画 */
.spinner {
    width: 40px;
    height: 40px;
    margin: 10px auto;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #0056b3;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(-20px); }
    to { opacity: 1; transform: translateY(0); }
}

/* 无任务页面 */
.notification-container {
    display: flex;
    justify-content: center;
    align-items: center;
    height: 100%;
}

.notification {
    background-color: rgba(255, 255, 255, 0.9);
    border-radius: 8px;
    box-shadow: 0 0 20px rgba(0, 0, 0, 0.2);
    padding: 30px;
    text-align: center;
    width: 400px;
}

.notification h2 {
    margin-bottom: 15px;
    color: #0056b3;
}

.notification p {
    margin-bottom: 20px;
    font-size: 16px;
} 
<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风电集团安全登录系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/style.css') }}">
</head>
<body>
    <div class="container">
        <div class="login-container">
            <div class="header">
                <img src="{{ url_for('static', filename='img/company_logo.png') }}" alt="公司Logo" class="logo">
                <h1>风电集团安全监控系统</h1>
            </div>
            <div class="login-form">
                <div class="form-group">
                    <label for="username">用户名：</label>
                    <input type="text" id="username" name="username" required>
                </div>
                <div class="form-group">
                    <label for="password">密码：</label>
                    <input type="password" id="password" name="password" required>
                </div>
                <div class="form-group">
                    <label>是否为二级公司：</label>
                    <div class="radio-group">
                        <label><input type="radio" name="is_secondary_company" value="true"> 是</label>
                        <label><input type="radio" name="is_secondary_company" value="false" checked> 否</label>
                    </div>
                </div>
                <div class="form-group">
                    <button id="login-btn" class="btn">登录系统</button>
                </div>
            </div>
        </div>
        
        <!-- 错误提示弹窗 -->
        <div id="error-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>错误</h2>
                    <span class="close">&times;</span>
                </div>
                <div class="modal-body">
                    <p id="error-message"></p>
                </div>
                <div class="modal-footer">
                    <button class="btn" id="ok-btn">确定</button>
                </div>
            </div>
        </div>
        
        <!-- 处理中提示框 -->
        <div id="loading-modal" class="modal">
            <div class="modal-content">
                <div class="modal-header">
                    <h2>正在处理</h2>
                </div>
                <div class="modal-body">
                    <div class="spinner"></div>
                    <p>正在初始化监控系统，请稍候...</p>
                </div>
            </div>
        </div>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            // 获取DOM元素
            const loginBtn = document.getElementById('login-btn');
            const errorModal = document.getElementById('error-modal');
            const loadingModal = document.getElementById('loading-modal');
            const errorMessage = document.getElementById('error-message');
            const closeButtons = document.querySelectorAll('.close');
            const okBtn = document.getElementById('ok-btn');
            
            // 添加登录按钮点击事件
            loginBtn.addEventListener('click', function() {
                const username = document.getElementById('username').value;
                const password = document.getElementById('password').value;
                const isSecondaryCompany = document.querySelector('input[name="is_secondary_company"]:checked').value;
                
                if (!username || !password) {
                    showErrorModal('用户名和密码不能为空！');
                    return;
                }
                
                // 显示加载动画
                showLoadingModal();
                
                // 发送登录请求
                fetch('/login', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/x-www-form-urlencoded',
                    },
                    body: `username=${encodeURIComponent(username)}&password=${encodeURIComponent(password)}&is_secondary_company=${encodeURIComponent(isSecondaryCompany)}`
                })
                .then(response => response.json())
                .then(data => {
                    if (data.status === 'error') {
                        hideLoadingModal();
                        showErrorModal(data.message);
                    } else {
                        // 登录成功，跳转到仪表盘页面
                        window.location.href = '/dashboard';
                    }
                })
                .catch(error => {
                    hideLoadingModal();
                    showErrorModal('请求发生错误：' + error.message);
                });
            });
            
            // 显示错误弹窗
            function showErrorModal(message) {
                errorMessage.textContent = message;
                errorModal.style.display = 'block';
            }
            
            // 显示加载动画
            function showLoadingModal() {
                loadingModal.style.display = 'block';
            }
            
            // 隐藏加载动画
            function hideLoadingModal() {
                loadingModal.style.display = 'none';
            }
            
            // 关闭弹窗的点击事件
            closeButtons.forEach(button => {
                button.addEventListener('click', function() {
                    const modal = button.closest('.modal');
                    if (modal) {
                        modal.style.display = 'none';
                    }
                });
            });
            
            okBtn.addEventListener('click', function() {
                errorModal.style.display = 'none';
            });
            
            // 点击弹窗外部关闭弹窗
            window.addEventListener('click', function(event) {
                if (event.target === errorModal) {
                    errorModal.style.display = 'none';
                }
            });
        });
    </script>
</body>
</html> 
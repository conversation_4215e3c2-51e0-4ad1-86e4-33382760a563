document.addEventListener('DOMContentLoaded', function() {
    // DOM元素
    const loadingModal = document.getElementById('loading-modal');
    const imageList = document.getElementById('image-list');
    const jobList = document.getElementById('job-list');
    const currentImage = document.getElementById('current-image');
    const imageJobName = document.getElementById('image-job-name');
    const imageJobLocation = document.getElementById('image-job-location');
    const cameraNamElement = document.getElementById('image-camera-name');
    const imageTimestamp = document.getElementById('image-timestamp');
    const imageReason = document.getElementById('image-reason');
    const prevImageBtn = document.getElementById('prev-image');
    const nextImageBtn = document.getElementById('next-image');
    const applyFilterBtn = document.getElementById('apply-filter');
    const startDateInput = document.getElementById('start-date');
    const endDateInput = document.getElementById('end-date');
    const stopMonitoringBtn = document.getElementById('stop-monitoring');
    const refreshDataBtn = document.getElementById('refresh-data');
    
    // 全局变量
    let abnormalImages = [];
    let currentImageIndex = -1;
    let pollingInterval = null;
    
    // 初始化日期选择器
    const today = new Date();
    const todayStr = formatDateForInput(today);
    startDateInput.value = todayStr;
    endDateInput.value = todayStr;
    
    // 初始化
    initPage();
    
    // 事件监听
    prevImageBtn.addEventListener('click', showPreviousImage);
    nextImageBtn.addEventListener('click', showNextImage);
    applyFilterBtn.addEventListener('click', loadAbnormalImages);
    stopMonitoringBtn.addEventListener('click', stopMonitoring);
    refreshDataBtn.addEventListener('click', refreshData);
    
    /**
     * 初始化页面
     */
    function initPage() {
        showLoading();
        
        // 加载作业信息
        loadJobInfo();
        
        // 加载异常图片
        loadAbnormalImages();
        
        // 开始实时数据轮询
        startPolling();
    }
    
    /**
     * 开始实时数据轮询
     */
    function startPolling() {
        // 清除可能存在的旧轮询
        if (pollingInterval) {
            clearInterval(pollingInterval);
        }
        
        // 每5秒轮询一次最新数据
        pollingInterval = setInterval(function() {
            fetch('/status')
                .then(response => response.json())
                .then(data => {
                    // 更新异常图片列表
                    if (data.abnormal_images && data.abnormal_images.length > 0) {
                        // 将新图片添加到列表
                        for (let newImage of data.abnormal_images) {
                            // 检查图片是否已存在
                            const exists = abnormalImages.some(img => img.filepath === newImage.filepath);
                            if (!exists) {
                                abnormalImages.unshift(newImage); // 添加到开头
                            }
                        }
                        // 更新图片列表显示
                        renderImageList();
                        
                        // 如果未选中图片，显示最新的图片
                        if (currentImageIndex === -1 && abnormalImages.length > 0) {
                            showImage(0);
                        }
                    }
                })
                .catch(error => console.error('数据轮询出错:', error));
        }, 5000);
    }
    
    /**
     * 加载异常图片列表
     */
    function loadAbnormalImages() {
        showLoading();
        
        const startDate = startDateInput.value;
        const endDate = endDateInput.value;
        
        let url = '/api/abnormal_images';
        if (startDate) {
            url += `?start_time=${startDate}`;
            if (endDate) {
                url += `&end_time=${endDate}`;
            }
        } else if (endDate) {
            url += `?end_time=${endDate}`;
        }
        
        fetch(url)
            .then(response => response.json())
            .then(data => {
                abnormalImages = data;
                renderImageList();
                
                // 显示第一张图片（如果有）
                if (abnormalImages.length > 0) {
                    showImage(0);
                } else {
                    clearImageDisplay();
                }
                hideLoading();
            })
            .catch(error => {
                console.error('获取异常图片列表失败:', error);
                hideLoading();
            });
    }
    
    /**
     * 加载作业信息
     */
    function loadJobInfo() {
        fetch('/api/job_info')
            .then(response => response.json())
            .then(data => {
                renderJobList(data);
            })
            .catch(error => {
                console.error('获取作业信息失败:', error);
            });
    }
    
    /**
     * 渲染图片列表
     */
    function renderImageList() {
        imageList.innerHTML = '';
        
        if (abnormalImages.length === 0) {
            imageList.innerHTML = '<p class="no-data">当前没有异常图片</p>';
            return;
        }
        
        for (let i = 0; i < abnormalImages.length; i++) {
            const image = abnormalImages[i];
            const listItem = document.createElement('div');
            listItem.className = 'image-list-item';
            if (i === currentImageIndex) {
                listItem.classList.add('selected');
            }
            
            // 格式化时间戳
            const formattedTimestamp = formatTimestamp(image.timestamp);
            
            // 组装图片完整路径 - 考虑子目录
            let imagePath;
            if (image.dir) {
                // 新格式: 使用目录
                imagePath = `/screenshots/${image.dir}/${image.filepath}`;
            } else {
                // 旧格式: 直接使用文件名
                imagePath = `/screenshots/${image.filepath}`;
            }
            
            listItem.innerHTML = `
                <img src="${imagePath}" alt="缩略图" class="image-list-item-thumbnail" onerror="this.src='/static/img/image_error.png'">
                <div class="image-list-item-info">
                    <h3>${image.job_name} - ${image.camera_name}</h3>
                    <p>地点: ${image.job_location}</p>
                    <p>时间: ${formattedTimestamp}</p>
                    <p>原因: ${image.reason}</p>
                </div>
            `;
            
            listItem.addEventListener('click', () => showImage(i));
            imageList.appendChild(listItem);
        }
    }
    
    /**
     * 渲染作业列表
     */
    function renderJobList(jobs) {
        jobList.innerHTML = '';
        
        if (jobs.length === 0) {
            jobList.innerHTML = '<p class="no-data">当前没有作业信息</p>';
            return;
        }
        
        for (const job of jobs) {
            const jobItem = document.createElement('div');
            jobItem.className = 'job-item';
            
            let cameraListHTML = '';
            if (job.cameras && job.cameras.length > 0) {
                cameraListHTML = '<ul class="camera-list">';
                for (const camera of job.cameras) {
                    cameraListHTML += `<li>${camera}</li>`;
                }
                cameraListHTML += '</ul>';
            }
            
            jobItem.innerHTML = `
                <h3>${job.job_name}</h3>
                <div class="job-item-info">
                    <p><strong>作业地点:</strong> ${job.job_location}</p>
                    <p><strong>摄像头数量:</strong> ${job.cameras ? job.cameras.length : 0}</p>
                </div>
                ${cameraListHTML}
            `;
            
            jobList.appendChild(jobItem);
        }
    }
    
    /**
     * 显示指定索引的图片
     */
    function showImage(index) {
        if (index < 0 || index >= abnormalImages.length) {
            return;
        }
        
        const image = abnormalImages[index];
        currentImageIndex = index;
        
        // 组装图片完整路径
        let imagePath;
        if (image.dir) {
            // 新格式: 使用目录
            imagePath = `/screenshots/${image.dir}/${image.filepath}`;
        } else {
            // 旧格式: 直接使用文件名
            imagePath = `/screenshots/${image.filepath}`;
        }
        
        // 更新图片显示
        currentImage.src = imagePath;
        currentImage.onerror = function() {
            // 如果图片加载失败，显示错误消息
            this.onerror = null;
            this.src = '/static/img/image_error.png';
            this.alt = '图片加载失败';
            console.error(`无法加载图片: ${imagePath}`);
        };
        
        imageJobName.textContent = `作业名称: ${image.job_name}`;
        imageJobLocation.textContent = `作业地点: ${image.job_location}`;
        cameraNamElement.textContent = `摄像头: ${image.camera_name}`;
        imageTimestamp.textContent = `时间: ${formatTimestamp(image.timestamp)}`;
        imageReason.textContent = `异常原因: ${image.reason || '未知'}`;
        
        // 更新图片列表的选中状态
        const items = imageList.querySelectorAll('.image-list-item');
        items.forEach((item, i) => {
            if (i === index) {
                item.classList.add('selected');
                item.scrollIntoView({ behavior: 'smooth', block: 'nearest' });
            } else {
                item.classList.remove('selected');
            }
        });
    }
    
    /**
     * 显示上一张图片
     */
    function showPreviousImage() {
        if (abnormalImages.length === 0) return;
        
        let newIndex = currentImageIndex - 1;
        if (newIndex < 0) {
            newIndex = abnormalImages.length - 1;
        }
        showImage(newIndex);
    }
    
    /**
     * 显示下一张图片
     */
    function showNextImage() {
        if (abnormalImages.length === 0) return;
        
        let newIndex = currentImageIndex + 1;
        if (newIndex >= abnormalImages.length) {
            newIndex = 0;
        }
        showImage(newIndex);
    }
    
    /**
     * 清空图片显示
     */
    function clearImageDisplay() {
        currentImage.src = '';
        imageJobName.textContent = '作业名称: ';
        imageJobLocation.textContent = '作业地点: ';
        cameraNamElement.textContent = '摄像头: ';
        imageTimestamp.textContent = '时间: ';
        imageReason.textContent = '异常原因: ';
        currentImageIndex = -1;
    }
    
    /**
     * 停止监控
     */
    function stopMonitoring() {
        if (confirm('确定要停止监控吗？')) {
            showLoading();
            fetch('/stop_monitoring', {
                method: 'POST'
            })
            .then(response => response.json())
            .then(data => {
                alert('监控已停止');
                hideLoading();
            })
            .catch(error => {
                console.error('停止监控失败:', error);
                alert('停止监控失败');
                hideLoading();
            });
        }
    }
    
    /**
     * 刷新数据
     */
    function refreshData() {
        loadJobInfo();
        loadAbnormalImages();
    }
    
    /**
     * 显示加载中状态
     */
    function showLoading() {
        loadingModal.style.display = 'flex';
    }
    
    /**
     * 隐藏加载中状态
     */
    function hideLoading() {
        loadingModal.style.display = 'none';
    }
    
    /**
     * 格式化时间戳
     */
    function formatTimestamp(timestamp) {
        if (!timestamp) return '';
        
        // 假设时间戳格式为 "YYYYMMDD_HHMMSS"
        const year = timestamp.slice(0, 4);
        const month = timestamp.slice(4, 6);
        const day = timestamp.slice(6, 8);
        const hour = timestamp.slice(9, 11);
        const minute = timestamp.slice(11, 13);
        const second = timestamp.slice(13, 15);
        
        return `${year}-${month}-${day} ${hour}:${minute}:${second}`;
    }
    
    /**
     * 为日期选择器格式化日期
     */
    function formatDateForInput(date) {
        const year = date.getFullYear();
        const month = String(date.getMonth() + 1).padStart(2, '0');
        const day = String(date.getDate()).padStart(2, '0');
        return `${year}-${month}-${day}`;
    }
}); 
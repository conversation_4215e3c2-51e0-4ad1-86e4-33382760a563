<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>风电集团安全监控系统</title>
    <link rel="stylesheet" href="{{ url_for('static', filename='css/dashboard.css') }}">
</head>
<body>
    <div class="container">
        <header>
            <div class="logo-container">
                <img src="{{ url_for('static', filename='img/company_logo.png') }}" alt="公司Logo" class="logo">
                <h1>风电集团安全监控系统</h1>
            </div>
            <div class="control-panel">
                <button id="stop-monitoring" class="btn danger">停止监控</button>
                <button id="refresh-data" class="btn primary">刷新数据</button>
            </div>
        </header>
        
        <div class="dashboard">
            <div class="left-panel">
                <div class="image-display-container">
                    <h2>异常图片预览</h2>
                    <div class="image-display">
                        <img id="current-image" src="" alt="暂无异常图片">
                        <div class="image-info">
                            <p id="image-job-name">作业名称：</p>
                            <p id="image-job-location">作业地点：</p>
                            <p id="image-camera-name">摄像头：</p>
                            <p id="image-timestamp">时间：</p>
                            <p id="image-reason">异常原因：</p>
                        </div>
                    </div>
                    <div class="image-controls">
                        <button id="prev-image" class="btn">上一张</button>
                        <button id="next-image" class="btn">下一张</button>
                    </div>
                </div>
                
                <div class="image-list-container">
                    <h2>异常图片列表</h2>
                    <div class="filter-container">
                        <div class="date-filter">
                            <label for="start-date">开始日期：</label>
                            <input type="date" id="start-date">
                            <label for="end-date">结束日期：</label>
                            <input type="date" id="end-date">
                            <button id="apply-filter" class="btn">应用筛选</button>
                        </div>
                    </div>
                    <div class="image-list" id="image-list">
                        <!-- 异常图片列表将通过JavaScript动态加载 -->
                    </div>
                </div>
            </div>
            
            <div class="right-panel">
                <h2>作业信息</h2>
                <div class="job-list" id="job-list">
                    <!-- 作业信息将通过JavaScript动态加载 -->
                </div>
            </div>
        </div>
        
        <!-- 加载中提示框 -->
        <div id="loading-modal" class="modal">
            <div class="modal-content">
                <div class="spinner"></div>
                <p>正在加载数据，请稍候...</p>
            </div>
        </div>
    </div>

    <script src="{{ url_for('static', filename='js/dashboard.js') }}"></script>
</body>
</html> 
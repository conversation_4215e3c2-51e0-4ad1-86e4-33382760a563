<html class=" "><head><meta charset="utf-8"><meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1"><meta name="renderer" content="webkit"><meta name="viewport" content="width=device-width,initial-scale=1,maximum-scale=1,user-scalable=no"><link rel="icon" href=""><title>安全管理系统</title><!--[if lt IE 11]><script>window.location.href='/html/ie.html';</script><![endif]--><style>html,
    body,
    #app {
      height: 100%;
      margin: 0px;
      padding: 0px;
    }
    .chromeframe {
      margin: 0.2em 0;
      background: #ccc;
      color: #000;
      padding: 0.2em 0;
    }

    #loader-wrapper {
      position: fixed;
      top: 0;
      left: 0;
      width: 100%;
      height: 100%;
      z-index: 999999;
    }

    #loader {
      display: block;
      position: relative;
      left: 50%;
      top: 50%;
      width: 150px;
      height: 150px;
      margin: -75px 0 0 -75px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 2s linear infinite;
      -ms-animation: spin 2s linear infinite;
      -moz-animation: spin 2s linear infinite;
      -o-animation: spin 2s linear infinite;
      animation: spin 2s linear infinite;
      z-index: 1001;
    }

    #loader:before {
      content: "";
      position: absolute;
      top: 5px;
      left: 5px;
      right: 5px;
      bottom: 5px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -webkit-animation: spin 3s linear infinite;
      -moz-animation: spin 3s linear infinite;
      -o-animation: spin 3s linear infinite;
      -ms-animation: spin 3s linear infinite;
      animation: spin 3s linear infinite;
    }

    #loader:after {
      content: "";
      position: absolute;
      top: 15px;
      left: 15px;
      right: 15px;
      bottom: 15px;
      border-radius: 50%;
      border: 3px solid transparent;
      border-top-color: #FFF;
      -moz-animation: spin 1.5s linear infinite;
      -o-animation: spin 1.5s linear infinite;
      -ms-animation: spin 1.5s linear infinite;
      -webkit-animation: spin 1.5s linear infinite;
      animation: spin 1.5s linear infinite;
    }


    @-webkit-keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }

    @keyframes spin {
      0% {
        -webkit-transform: rotate(0deg);
        -ms-transform: rotate(0deg);
        transform: rotate(0deg);
      }
      100% {
        -webkit-transform: rotate(360deg);
        -ms-transform: rotate(360deg);
        transform: rotate(360deg);
      }
    }


    #loader-wrapper .loader-section {
      position: fixed;
      top: 0;
      width: 51%;
      height: 100%;
      background: #7171C6;
      z-index: 1000;
      -webkit-transform: translateX(0);
      -ms-transform: translateX(0);
      transform: translateX(0);
    }

    #loader-wrapper .loader-section.section-left {
      left: 0;
    }

    #loader-wrapper .loader-section.section-right {
      right: 0;
    }


    .loaded #loader-wrapper .loader-section.section-left {
      -webkit-transform: translateX(-100%);
      -ms-transform: translateX(-100%);
      transform: translateX(-100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader-wrapper .loader-section.section-right {
      -webkit-transform: translateX(100%);
      -ms-transform: translateX(100%);
      transform: translateX(100%);
      -webkit-transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
      transition: all 0.7s 0.3s cubic-bezier(0.645, 0.045, 0.355, 1.000);
    }

    .loaded #loader {
      opacity: 0;
      -webkit-transition: all 0.3s ease-out;
      transition: all 0.3s ease-out;
    }

    .loaded #loader-wrapper {
      visibility: hidden;
      -webkit-transform: translateY(-100%);
      -ms-transform: translateY(-100%);
      transform: translateY(-100%);
      -webkit-transition: all 0.3s 1s ease-out;
      transition: all 0.3s 1s ease-out;
    }

    .no-js #loader-wrapper {
      display: none;
    }

    .no-js h1 {
      color: #222222;
    }

    #loader-wrapper .load_title {
      font-family: 'Open Sans';
      color: #FFF;
      font-size: 19px;
      width: 100%;
      text-align: center;
      z-index: 9999999999999;
      position: absolute;
      top: 60%;
      opacity: 1;
      line-height: 30px;
    }

    #loader-wrapper .load_title span {
      font-weight: normal;
      font-style: italic;
      font-size: 13px;
      color: #FFF;
      opacity: 0.5;
    }</style>
                <video autoplay="" id="player_playVideo0" class="play-window" wid="0" width="396" height="296" style="object-fit: fill; display: none;">
                </video>
                <canvas id="player_playCanvas0" class="play-window" wid="0" width="792" height="592" style="transform: scale(0.5); transform-origin: 0 0;">
                </canvas>
                <canvas id="player_canvas_draw0" class="draw-window" wid="0" width="396" height="296" style="position: absolute; top: 0; left: 0;">
                </canvas>
            </div></div></div><canvas id="myCanvas" style="display: none;"></canvas><div class="el-tooltip shot item" aria-describedby="el-tooltip-5276" tabindex="0">抓拍</div></div><div data-v-6305a3b4="" class="bottom"><div data-v-6305a3b4="" class="el-select el-select--small"><!----><div class="el-input el-input--small el-input--suffix"><!----><input type="text" readonly="readonly" autocomplete="off" placeholder="请选择" class="el-input__inner"><!----><span class="el-input__suffix"><span class="el-input__suffix-inner"><i class="el-select__caret el-input__icon el-icon-arrow-up"></i><!----><!----><!----><!----><!----></span><!----></span><!----><!----></div><div class="el-select-dropdown el-popper" style="display: none; min-width: 224.167px;"><div class="el-scrollbar" style=""><div class="el-select-dropdown__wrap el-scrollbar__wrap" style="margin-bottom: -7px; margin-right: -7px;"><ul class="el-scrollbar__view el-select-dropdown__list"><!----><li data-v-6305a3b4="" class="el-select-dropdown__item selected"><span>6替替电除尘塔吊照南</span></li><li data-v-6305a3b4="" class="el-select-dropdown__item"><span>5二号塔吊西北角</span></li></ul></div><div class="el-scrollbar__bar is-horizontal"><div class="el-scrollbar__thumb" style="transform: translateX(0%);"></div></div><div class="el-scrollbar__bar is-vertical"><div class="el-scrollbar__thumb" style="transform: translateY(0%);"></div></div></div><!----></div></div><div data-v-6305a3b4=""><button data-v-6305a3b4="" type="button" class="el-button el-button--primary el-button--small"><!----><i class="el-icon-plus"></i><span>问题记录 </span></button></div></div></div></div><div data-v-6305a3b4="" class="item"><div data-v-7c6bcee3="" data-v-6305a3b4="" class="label"><span data-v-7c6bcee3="">问题记录</span></div><div data-v-6305a3b4="" class="mode_box"><div data-v-6305a3b4="" class="violation flex"><ul data-v-6305a3b4="" class="problem-list"></ul></div></div></div></div></div><div data-v-6305a3b4="" class="item"><div data-v-7c6bcee3="" data-v-6305a3b4="" class="label"><span data-v-7c6bcee3="">监管人员到岗到位</span></div><div data-v-6305a3b4="" class="mode_box"><div data-v-6305a3b4="" class="already-register el-descriptions"><div class="el-descriptions__body"><table class="el-descriptions__table el-descriptions--medium"><tbody><tr class="el-descriptions-row"><td colspan="1" class="el-descriptions-item el-descriptions-item__cell"><div class="el-descriptions-item__container"><span class="el-descriptions-item__label has-colon " style="width: 240px; display: inline-block;">主管部门监管人员</span><span class="el-descriptions-item__content"><div data-v-6305a3b4=""><div data-v-6305a3b4="" style="margin-right: 16px;"><div data-v-6305a3b4=""> 安林伟 <span data-v-6305a3b4="" style="color: rgb(38, 171, 49);"> (2025-07-31  12:21:12) </span></div></div><div data-v-6305a3b4="" style="margin-right: 16px;"><div data-v-6305a3b4=""> 吕圣楠 <span data-v-6305a3b4="" style="color: rgb(38, 171, 49);"> (2025-07-31  12:21:32) </span></div></div><div data-v-6305a3b4="" style="margin-right: 16px;"><div data-v-6305a3b4=""> 陈海涛 <span data-v-6305a3b4="" style="color: rgb(255, 0, 0);">(未签到)</span></div></div></div></span></div></td><td colspan="1" class="el-descriptions-item el-descriptions-item__cell"><div class="el-descriptions-item__container"><span class="el-descriptions-item__label has-colon " style="width: 240px; display: inline-block;">安全监察部门监管人员</span><span class="el-descriptions-item__content"><div data-v-6305a3b4=""><div data-v-6305a3b4="" style="margin-right: 16px;"><div data-v-6305a3b4=""> 吴扶瑶 <span data-v-6305a3b4="" style="color: rgb(38, 171, 49);"> (2025-07-31  12:23:34) </span></div></div></div></span></div></td></tr></tbody><tbody><tr class="el-descriptions-row"><td colspan="2" class="el-descriptions-item el-descriptions-item__cell"><div class="el-descriptions-item__container"><span class="el-descriptions-item__label has-colon " style="width: 240px; display: inline-block;">分管领导</span><span class="el-descriptions-item__content"><div data-v-6305a3b4=""><div data-v-6305a3b4="" style="margin-right: 16px;"><div data-v-6305a3b4=""> 米强 <span data-v-6305a3b4="" style="color: rgb(38, 171, 49);"> (2025-07-31  12:21:38) </span></div></div></div></span></div></td></tr></tbody></table></div></div></div></div><div data-v-6305a3b4="" class="item"><div data-v-7c6bcee3="" data-v-6305a3b4="" class="label"><span data-v-7c6bcee3="">作业文件</span></div><div data-v-6305a3b4="" class="mode_box"><div data-v-6305a3b4="" class="scroll"><div data-v-6305a3b4="" class="item_1 flex"><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remark">危险作业项目评估审批单:</div><span data-v-6305a3b4="" class="ml10">暂无文件</span></div><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remarkqp" style="margin-left: 10px;">签批页:</div><span data-v-6305a3b4="">暂无文件</span></div></div><div data-v-6305a3b4="" class="item_1 flex"><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remark">三措两案:</div><a data-v-6305a3b4="" class="link-item el-link el-link--default is-underline" style="font-size: 16px; max-width: 375px;"><!----><span class="el-link--inner">国能博州2号机组锅炉钢架平台安装专项方案_20250516161735A662_20250725122241A066.pdf</span><!----></a></div><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remarkqp" style="margin-left: 10px;">签批页:</div><a data-v-6305a3b4="" class="link-item el-link el-link--default is-underline" style="font-size: 16px; max-width: 375px;"><!----><span class="el-link--inner">审批_20250516161746A655_20250725122250A486.png</span><!----></a></div></div><div data-v-6305a3b4="" class="item_1 flex"><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remark">三措两案培训记录表:</div><a data-v-6305a3b4="" class="link-item el-link el-link--default is-underline" style="font-size: 16px; max-width: 375px;"><!----><span class="el-link--inner">国能博州2号机组锅炉钢架平台安装专项方案_20250516161727A649_20250725122029A473.pdf</span><!----></a></div><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remarkqp" style="margin-left: 10px;">签批页:</div><span data-v-6305a3b4="">暂无文件</span></div></div><div data-v-6305a3b4="" class="item_1 flex"><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remark">应急演练记录:</div><a data-v-6305a3b4="" class="link-item el-link el-link--default is-underline" style="font-size: 16px; max-width: 375px;"><!----><span class="el-link--inner">国能博州2号机组锅炉钢架吊装交底旁站记录_20250516161719A292_20250725121824A364.pdf</span><!----></a></div><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remarkqp" style="margin-left: 10px;">签批页:</div><span data-v-6305a3b4="">暂无文件</span></div></div><div data-v-6305a3b4="" class="item_1 flex"><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remark">作业专项安全交底:</div><a data-v-6305a3b4="" class="link-item el-link el-link--default is-underline" style="font-size: 16px; max-width: 375px;"><!----><span class="el-link--inner">2号锅炉钢架吊装安全技术交底_20250725123449A467.pdf</span><!----></a></div><div data-v-6305a3b4="" style="width: 50%;"><div data-v-6305a3b4="" class="remarkqp" style="margin-left: 10px;">签批页:</div><span data-v-6305a3b4="">暂无文件</span></div></div></div></div></div></main><div data-v-6305a3b4="" class="el-dialog__wrapper" style="display: none;"><div role="dialog" aria-modal="true" aria-label="签到" class="el-dialog" style="margin-top: 0px; position: absolute; left: 50%; top: 50%; transform: translate(-50%, -50%);"><div class="el-dialog__header" style="cursor: move;"><span class="el-dialog__title">签到</span><button type="button" aria-label="Close" class="el-dialog__headerbtn"><i class="el-dialog__close el-icon el-icon-close"></i></button></div><!----><div class="el-dialog__footer"><div data-v-6305a3b4="" class="dialog-footer"><button data-v-6305a3b4="" type="button" class="el-button el-button--primary el-button--small"><!----><!----><span>签 到</span></button><button data-v-6305a3b4="" type="button" class="el-button el-button--default el-button--small"><!----><!----><span>取 消</span></button></div></div></div></div><!----><!----></div><span data-v-0411e39e="" mode="out-in"></span></section><div data-v-120e6024="" class="rightPanel-container"><div data-v-120e6024="" class="rightPanel-background"></div><div data-v-120e6024="" class="rightPanel"><div data-v-120e6024="" class="rightPanel-items"><div data-v-488e8686="" tabindex="-1" class="el-drawer__wrapper" data-v-120e6024="" style="display: none;"><div role="document" tabindex="-1" class="el-drawer__container"><div aria-modal="true" aria-labelledby="el-drawer__title" aria-label="" role="dialog" tabindex="-1" class="el-drawer rtl" style="width: 280px;"><!----><!----></div></div></div></div></div></div></div></div><div data-v-1182d5b0="" class="theme-picker el-color-picker el-color-picker--medium"><!----><div class="el-color-picker__trigger"><span class="el-color-picker__color"><span class="el-color-picker__color-inner" style="background-color: rgb(64, 158, 255);"></span><!----></span><span class="el-color-picker__icon el-icon-arrow-down"></span></div><div class="el-color-dropdown el-color-picker__panel theme-picker-dropdown" style="display: none;"><div class="el-color-dropdown__main-wrapper"><div class="el-color-hue-slider is-vertical" style="float: right;"><div class="el-color-hue-slider__bar"></div><div class="el-color-hue-slider__thumb" style="left: 0px; top: 0px;"></div></div><div class="el-color-svpanel" style="background-color: rgb(0, 125, 255);"><div class="el-color-svpanel__white"></div><div class="el-color-svpanel__black"></div><div class="el-color-svpanel__cursor" style="top: 0px; left: 0px;"><div></div></div></div></div><!----><div class="el-color-predefine"><div class="el-color-predefine__colors"><div class="el-color-predefine__color-selector selected"><div style="background-color: rgb(64, 158, 255);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(24, 144, 255);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(48, 65, 86);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(33, 33, 33);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(17, 169, 131);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(19, 194, 194);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(105, 89, 205);"></div></div><div class="el-color-predefine__color-selector"><div style="background-color: rgb(245, 34, 45);"></div></div></div></div><div class="el-color-dropdown__btns"><span class="el-color-dropdown__value"><div class="el-input el-input--mini"><!----><input type="text" autocomplete="off" class="el-input__inner"><!----><!----><!----><!----></div></span><button type="button" class="el-button el-color-dropdown__link-btn el-button--text el-button--mini"><!----><!----><span>
        清空
      </span></button><button type="button" class="el-button el-color-dropdown__btn el-button--default el-button--mini is-plain"><!----><!----><span>
        确定
      </span></button></div></div></div></div></body></html>